package com.cosfo.oms.facade;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderOmsQueryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderQueryFacade {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;

    /**
     * 根据id 查询订单信息
     *
     * @param orderId
     * @return
     */
    public OrderResp queryById(Long orderId) {
        DubboResponse<OrderResp> response = orderQueryProvider.queryById(orderId);
        if (!response.isSuccess()) {
            log.error("查询订单信息失败, orderId: {}, result: {}", orderId, JSON.toJSONString(response));
            throw new ProviderException("查询订单信息失败");
        }
        return response.getData();
    }

    /**
     * 根据id批量查询
     * @param orderIds
     * @return
     */
    public List<OrderResp> queryByIds(List<Long> orderIds) {
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryByIds(orderIds);
        if (!response.isSuccess()) {
            log.error("查询订单信息失败, orderIds: {}, result: {}", orderIds, JSON.toJSONString(response));
            throw new ProviderException("查询订单信息失败");
        }
        return response.getData();
    }


    /**
     * 查询订单列表
     * @return
     */
    public List<OrderResp> queryOrderList(OrderQueryReq orderQueryReq) {
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryOrderList(orderQueryReq);
        if (!response.isSuccess()) {
            log.error("查询订单信息失败, result: {}", JSON.toJSONString(response));
            throw new ProviderException("查询订单信息失败");
        }
        return response.getData();
    }

    /**
     * 分页查询订单
     * @return
     */
    public PageInfo<OrderResp> queryOmsOrderPage(OrderOmsQueryReq orderOmsQueryReq) {
        DubboResponse<PageInfo<OrderResp>> response = orderQueryProvider.queryOmsOrderPage(orderOmsQueryReq);
        if (!response.isSuccess()) {
            log.error("查询订单信息失败, result: {}", JSON.toJSONString(response));
            throw new ProviderException("查询订单信息失败");
        }
        return response.getData();
    }

}
