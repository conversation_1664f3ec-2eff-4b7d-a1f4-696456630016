package com.cosfo.oms.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.oms.tenant.model.dto.FunctionSetQueryDTO;
import com.cosfo.oms.tenant.model.po.TenantFunctionSet;
import com.cosfo.oms.tenant.mapper.TenantFunctionSetMapper;
import com.cosfo.oms.tenant.dao.TenantFunctionSetDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 功能集 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
@Service
public class TenantFunctionSetDaoImpl extends ServiceImpl<TenantFunctionSetMapper, TenantFunctionSet> implements TenantFunctionSetDao {

    @Override
    public Page<TenantFunctionSet> selectByPage(FunctionSetQueryDTO queryDTO) {
        QueryWrapper<TenantFunctionSet> queryWrapper = new QueryWrapper<>();
        if (Boolean.TRUE.equals(queryDTO.getShowSaleVersion())) {
            queryWrapper.orderByAsc("`sale_version` is null", "sale_version");
        }
        queryWrapper.orderByDesc("create_time");
        LambdaQueryWrapper<TenantFunctionSet> lambdaQueryWrapper = queryWrapper.lambda();
        lambdaQueryWrapper.eq(queryDTO.getId() != null, TenantFunctionSet::getId, queryDTO.getId());
        lambdaQueryWrapper.isNull(!queryDTO.getShowSaleVersion(), TenantFunctionSet::getSaleVersion);
//        queryWrapper.orderByDesc(TenantFunctionSet::getUpdateTime);
        return page(new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize()), queryWrapper);
    }

    @Override
    public List<TenantFunctionSet> list(FunctionSetQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantFunctionSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(!StringUtils.isEmpty(queryDTO.getName()), TenantFunctionSet::getName, queryDTO.getName());
        queryWrapper.isNull(!queryDTO.getShowSaleVersion(), TenantFunctionSet::getSaleVersion);
        return list(queryWrapper);
    }

    @Override
    public TenantFunctionSet detail(Long id) {
        return getById(id);
    }

    @Override
    public Boolean add(TenantFunctionSet tenantFunctionSet) {
        return save(tenantFunctionSet);
    }

    @Override
    public Boolean update(TenantFunctionSet tenantFunctionSet) {
        return updateById(tenantFunctionSet);
    }

    @Override
    public Boolean delete(Long id) {
        return removeById(id);
    }

    @Override
    public TenantFunctionSet getFunctionSetBySaleVersion(Integer saleVersion) {
        LambdaQueryWrapper<TenantFunctionSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantFunctionSet::getSaleVersion, saleVersion);
        return getOne(queryWrapper);
    }

    @Override
    public boolean isExistName(String name, Long id) {
        LambdaQueryWrapper<TenantFunctionSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantFunctionSet::getName, name);
        queryWrapper.ne(id != null, TenantFunctionSet::getId, id);
        TenantFunctionSet one = getOne(queryWrapper);
        if (one != null) {
            return true;
        }
        return false;
    }
}
