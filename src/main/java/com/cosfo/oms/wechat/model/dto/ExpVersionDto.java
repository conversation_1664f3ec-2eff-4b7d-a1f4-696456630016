package com.cosfo.oms.wechat.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExpVersionDto implements Serializable {

    /**
     * 授权人APPID
     */
    private String appid;

    /**
     * 版本状态
     */
    private Integer pkgStatus;

    /**
     * 版本号
     */
    private String version;

    /**
     * 驳回原因
     */
    private String remark;

    /**
     * 版本描述
     */
    private String pkgDesc;

    /**
     * 对应模板id
     */
    private Integer templateId;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
