package com.cosfo.oms.tenant.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 租户运费规则实体
 *
 * <AUTHOR>
 */
@Data
public class TenantDeliveryFeeRule implements Serializable {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0、采用鲜沐运费 1、免运费 2,自定义
     */
    private Integer type;

    /**
     * 默认运费金额
     */
    private BigDecimal defaultPrice;

    /**
     * 满减限制
     */
    private BigDecimal freeNumber;

    /**
     * 满减类型 0按金额 1按限制
     */
    private Integer freeType;
    /**
     * 操作人
     */
    private String operator;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
