package com.cosfo.oms.order.mapper;

import com.cosfo.oms.order.model.po.OrderAddress;
import com.cosfo.summerfarm.model.dto.order.OrderAddressVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Deprecated
public interface OrderAddressMapper {

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(OrderAddress record);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insertSelective(OrderAddress record);

    /**
     * 查询
     *
     * @param id
     * @return
     */
    OrderAddress selectByPrimaryKey(Long id);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderAddress record);

    /**
     * genxg更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderAddress record);

    /**
     * 查询订单地址信息
     *
     * @param orderId
     * @param tenantId
     * @return
     */
    OrderAddress selectByOrderId(@Param("orderId") Long orderId, @Param("tenantId") Long tenantId);

    /**
     * 批量查询订单地址信息
     *
     * @param orderIds
     * @return
     */
    List<OrderAddress> selectByOrderIds(@Param("orderIds") List<Long> orderIds);
}
