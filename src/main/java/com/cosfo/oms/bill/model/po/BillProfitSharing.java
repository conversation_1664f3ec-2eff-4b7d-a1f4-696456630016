package com.cosfo.oms.bill.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * bill_profit_sharing
 *
 * <AUTHOR>

@Data
public class BillProfitSharing implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * appId
     */
    private String appId;

    /**
     * 账号类型
     */
    private String type;

    /**
     * 付款方账号
     */
    private String account;

    /**
     * 交易流水号
     */
    private String transactionId;

    /**
     * 外部交易订单号
     */
    private String outTradeNo;

    /**
     * 交易金额
     */
    private BigDecimal price;

    /**
     * 状态0待分账1成功2失败3取消
     */
    private Integer status;

    /**
     * 成功时间
     */
    private Date successTime;

    /**
     * 分账描述
     */
    private String description;

    /**
     * 微信分账单号
     */
    private String wxOrderId;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 分账明细单号
     */
    private String detailId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}