package com.cosfo.oms.service;

import com.cosfo.oms.model.dto.FileDownloadRecordDTO;
import com.cosfo.oms.model.dto.PageInfoDTO;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/20
 */
public interface FileDownloadService {

    /**
     * 查询下载历史
     *
     * @param fileDownloadRecordDTO
     * @return
     */
    CommonResult queryDownloadHistory(FileDownloadRecordDTO fileDownloadRecordDTO);

    /**
     * 删除导出历史
     *
     * @param id
     * @return
     */
    CommonResult deleteDownloadHistory(Long id);

    /**
     * 类型
     *
     * @param type
     * @return
     */
    CommonResult<String> downloadTemplate(Integer type);
}
