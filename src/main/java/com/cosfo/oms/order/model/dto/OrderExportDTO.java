package com.cosfo.oms.order.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Author: fansongsong
 * @Date: 2023-04-12
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderExportDTO {

    /**
     * 商城ID
     */
    private Long tenantId;
    /**
     * 商城名称
     */
    private String tenantName;
    /**
     * 订单编码
     */
    private String orderNO;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 支付方式
     */
    private String payType;
    /**
     * 收货人
     */
    private String contactName;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 地址
     */
    private String address;
    /**
     * sku
     */
    private Long itemId;

    /**
     * 自有编码
     */
    private String itemCode;
    /**
     * 商店名称
     */
    private String title;

    /**
     * 货源
     */
    private String goodsType;
    /**
     * 供应商
     */
    private String supplierName;
    /**
     * 商品规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 数量
     */
    private Integer amount;
    /**
     * 门店类型
     */
    private String storeTypeDesc;
    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 一级类目
     */
    private String firstCategoryName;

    /**
     * 二级类目
     */
    private String secondCategoryName;

    /**
     * 三级类目
     */
    private String thirdCategoryName;

    /**
     * 一级分类
     */
    private String firstClassificationName;

    /**
     * 二级分类
     */
    private String secondClassificationName;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;
    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 门店分组
     */
    private String groupName;
    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 外部系统订单号
     */
    private String customerOrderId;

}
