package com.cosfo.oms.controller;

import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.model.dto.CategoryDTO;
import com.cosfo.oms.service.CategoryService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 描述: 类目控制类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/8
 */
@RestController
@RequestMapping(value = "category")
public class CategoryController {

    @Resource
    private CategoryService categoryService;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public ResultDTO add(@RequestBody CategoryDTO categoryDTO) {
        return categoryService.add(categoryDTO);
    }

    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public ResultDTO edit(@RequestBody CategoryDTO categoryDTO) {
        return categoryService.edit(categoryDTO);
    }

    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    public ResultDTO remove(Long id) {
        return categoryService.remove(id);
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResultDTO list(String name, Integer type) {
        return categoryService.list(name, type);
    }
}
