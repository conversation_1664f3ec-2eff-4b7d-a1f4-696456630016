package com.cosfo.oms.merchant.service.impl;

import com.cosfo.oms.common.constant.NumberConstants;
import com.cosfo.oms.merchant.mapper.MerchantDeliveryFeeRuleMapper;
import com.cosfo.oms.merchant.mapper.MerchantDeliveryStepFeeMapper;
import com.cosfo.oms.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.oms.merchant.model.po.MerchantDeliveryStepFee;
import com.cosfo.oms.merchant.service.MerchantDeliveryFeeRuleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static com.cosfo.oms.common.constant.TenantDefaultConstant.*;

/**
 * @author: monna.chen
 * @Date: 2023/3/28 14:36
 * @Description:
 */
@Service
public class MerchantDeliveryFeeRuleServiceImpl implements MerchantDeliveryFeeRuleService {
    @Resource
    private MerchantDeliveryFeeRuleMapper merchantDeliveryFeeRuleMapper;
    @Resource
    private MerchantDeliveryStepFeeMapper merchantDeliveryStepFeeMapper;

    @Override
    public void insertDeliveryFeeRule(Long tenantId) {
        List<Integer> warehouseList = Arrays.asList(PROPRIETARY_TYPE, THREE_PARTIES_TYPE, SELF_WAREHOUSE_TYPE);
        for (Integer warehouseType : warehouseList) {
            MerchantDeliveryFeeRule feeRule = new MerchantDeliveryFeeRule();
            feeRule.setTenantId(tenantId);
            feeRule.setPriceType(DEFAULT_PRICE_TYPE);
            feeRule.setRelateNumber(DELIVERY_DEFAULT_FEE);
            feeRule.setFreeDeliveryType(DEFAULT_PRICE_TYPE);
            feeRule.setType(warehouseType);
            feeRule.setRuleType(THREE_PARTIES_TYPE.equals(warehouseType) ? DAY_RULE_TYPE : ORDER_RULE_TYPE);
            feeRule.setUpdateTime(LocalDateTime.now());
            feeRule.setPriority(NumberConstants.ONE);
            merchantDeliveryFeeRuleMapper.insert(feeRule);

            MerchantDeliveryStepFee stepFee = new MerchantDeliveryStepFee();
            stepFee.setTenantId(tenantId);
            stepFee.setRuleId(feeRule.getId());
            stepFee.setFeeRule(DEFAULT_PRICE_TYPE);
            stepFee.setStepThreshold(DELIVERY_DEFAULT_FEE);
            stepFee.setDeliveryFee(DELIVERY_DEFAULT_FEE);
            merchantDeliveryStepFeeMapper.insert(stepFee);
        }
    }
}
