package com.cosfo.oms.tenant.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class TenantPrivilegesConfigVO implements Serializable {

    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 功能集Id
     */
    private Long functionSetId;

    /**
     * 功能集名称
     */
    private String name;

    /**
     * 功能集描述
     */
    private String desc;

    /**
     * 过期时间
     */
    private LocalDate expireDate;

    /**
     * 标识 0:默认，1试用
     */
    private Integer flagType;

    /**
     * 0租户版本,1增购功能
     */
    private Integer configType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 生效时间
     */
    private LocalDate effectiveTime;
}
