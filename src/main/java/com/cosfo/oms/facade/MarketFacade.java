package com.cosfo.oms.facade;

import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.req.MarketItemCommonQueryReq;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.page.PageResp;
import com.cosfo.common.util.RpcResponseUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @author: monna.chen
 * @Date: 2023/4/27 17:53
 * @Description:
 */
@Service
@Slf4j
public class MarketFacade {
    @DubboReference
    private MarketProvider marketProvider;
    @DubboReference
    private MarketItemProvider marketItemProvider;


    public Map<Long, List<MarketItemInfoResp>> queryMarketItemListBySkuIds(Long tenantId, List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        DubboResponse<List<MarketItemInfoResp>> dubboResponse = marketProvider.queryMarketItemInfoBySkuIds(tenantId, skuIds);
        List<MarketItemInfoResp> list = RpcResponseUtil.handler(dubboResponse);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(MarketItemInfoResp::getSkuId));
    }

    public void soldOutBatchBySkuIds(Set<Long> skuId, Long tenantId) {
        DubboResponse dubboResponse = marketItemProvider.saleMarketItemInfoBySkuIds(tenantId, skuId, OnSaleTypeEnum.SOLD_OUT);
        RpcResponseUtil.handler(dubboResponse);
    }

    public List<MarketItemInfoResp> queryMarketItemList(Long tenantId, String title, Long itemId) {
        MarketItemCommonQueryReq req = new MarketItemCommonQueryReq();
        req.setTenantId (tenantId);
        req.setItemTitle (title);
        req.setItemId (itemId);
        req.setPageNum(1);
        req.setPageSize(1000);
        PageResp<MarketItemInfoResp> itemInfoRespPageResp = RpcResponseUtil.handler(marketItemProvider.queryMarketItemList (req));
        if (itemInfoRespPageResp == null || CollectionUtils.isEmpty(itemInfoRespPageResp.getList())) {
            return Collections.emptyList();
        }
        return itemInfoRespPageResp.getList();
//        MarketItemCommonQueryReq req = new MarketItemCommonQueryReq();
//        req.setItemTitle(title);
//        req.setItemId(itemId);
//        req.setTenantId(tenantId);
//        req.setPageNum(1);
//        req.setPageSize(1000);
//
//        PageResp<MarketItemInfoResp> itemInfoRespPageResp = RpcResponseUtil.handler(marketProvider.querySimpleMarketItemList(req));
//        if (itemInfoRespPageResp == null || CollectionUtils.isEmpty(itemInfoRespPageResp.getList())) {
//            return Collections.emptyList();
//        }
//        return itemInfoRespPageResp.getList();
    }
}
