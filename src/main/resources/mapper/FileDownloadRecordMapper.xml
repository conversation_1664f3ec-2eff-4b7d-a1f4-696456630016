<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.oms.mapper.FileDownloadRecordMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.oms.model.po.FileDownloadRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="params" jdbcType="VARCHAR" property="params"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, tenant_id, url, params, create_time, update_time, `type`, `status`
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from file_download_record
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from file_download_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.oms.model.po.FileDownloadRecord"
            useGeneratedKeys="true">
        insert into file_download_record (tenant_id, url, params,
        create_time, update_time, `type`,
        `status`)
        values (#{tenantId,jdbcType=BIGINT}, #{url,jdbcType=VARCHAR}, #{params,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{type,jdbcType=TINYINT},
        #{status,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.oms.model.po.FileDownloadRecord" useGeneratedKeys="true">
        insert into file_download_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="params != null">
                params,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="status != null">
                `status`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="params != null">
                #{params,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.oms.model.po.FileDownloadRecord">
        update file_download_record
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="params != null">
                params = #{params,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.oms.model.po.FileDownloadRecord">
        update file_download_record
        set tenant_id = #{tenantId,jdbcType=BIGINT},
        url = #{url,jdbcType=VARCHAR},
        params = #{params,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        `type` = #{type,jdbcType=TINYINT},
        `status` = #{status,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="queryFileDownloadRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from
        file_download_record
        where `tenant_id` = 0
        <if test="type != null">
            and `type` = #{type}
        </if>
        order by id desc
    </select>
</mapper>