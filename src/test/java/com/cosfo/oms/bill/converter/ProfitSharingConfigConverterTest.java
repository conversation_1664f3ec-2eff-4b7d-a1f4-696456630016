package com.cosfo.oms.bill.converter;

import com.cosfo.oms.bill.model.dto.ProfitSharingConfigDTO;
import com.cosfo.oms.bill.model.po.BillProfitSharingConfig;
import com.cosfo.oms.bill.model.vo.ProfitSharingConfigSaveVO;
import com.cosfo.oms.bill.model.vo.ProfitSharingConfigVO;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分账配置转换器测试类
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
class ProfitSharingConfigConverterTest {

    @Test
    void testVoToDto() {
        // Given
        ProfitSharingConfigVO vo = new ProfitSharingConfigVO();
        vo.setId(1L);
        vo.setTenantId(1001L);
        vo.setProfitSharingMode(2);
        vo.setProfitSharingSwitch(1);
        vo.setCreatedBy("test");
        vo.setUpdatedBy("test");

        // When
        ProfitSharingConfigDTO dto = ProfitSharingConfigConverter.voToDto(vo);

        // Then
        assertNotNull(dto);
        assertEquals(vo.getId(), dto.getId());
        assertEquals(vo.getTenantId(), dto.getTenantId());
        assertEquals(vo.getProfitSharingMode(), dto.getProfitSharingMode());
        assertEquals(vo.getProfitSharingSwitch(), dto.getProfitSharingSwitch());
        assertEquals(vo.getCreatedBy(), dto.getCreatedBy());
        assertEquals(vo.getUpdatedBy(), dto.getUpdatedBy());
    }

    @Test
    void testVoToDto_Null() {
        // When
        ProfitSharingConfigDTO dto = ProfitSharingConfigConverter.voToDto(null);

        // Then
        assertNull(dto);
    }

    @Test
    void testDtoToVo() {
        // Given
        ProfitSharingConfigDTO dto = new ProfitSharingConfigDTO();
        dto.setId(1L);
        dto.setTenantId(1001L);
        dto.setProfitSharingMode(2);
        dto.setProfitSharingSwitch(1);
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setCreatedBy("test");
        dto.setUpdatedBy("test");

        // When
        ProfitSharingConfigVO vo = ProfitSharingConfigConverter.dtoToVo(dto);

        // Then
        assertNotNull(vo);
        assertEquals(dto.getId(), vo.getId());
        assertEquals(dto.getTenantId(), vo.getTenantId());
        assertEquals(dto.getProfitSharingMode(), vo.getProfitSharingMode());
        assertEquals(dto.getProfitSharingSwitch(), vo.getProfitSharingSwitch());
        assertEquals(dto.getCreateTime(), vo.getCreateTime());
        assertEquals(dto.getUpdateTime(), vo.getUpdateTime());
        assertEquals(dto.getCreatedBy(), vo.getCreatedBy());
        assertEquals(dto.getUpdatedBy(), vo.getUpdatedBy());
    }

    @Test
    void testDtoToVo_Null() {
        // When
        ProfitSharingConfigVO vo = ProfitSharingConfigConverter.dtoToVo(null);

        // Then
        assertNull(vo);
    }

    @Test
    void testPoToDto() {
        // Given
        BillProfitSharingConfig po = new BillProfitSharingConfig();
        po.setId(1L);
        po.setTenantId(1001L);
        po.setProfitSharingMode(2);
        po.setProfitSharingSwitch(1);
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());
        po.setCreatedBy("test");
        po.setUpdatedBy("test");

        // When
        ProfitSharingConfigDTO dto = ProfitSharingConfigConverter.poToDto(po);

        // Then
        assertNotNull(dto);
        assertEquals(po.getId(), dto.getId());
        assertEquals(po.getTenantId(), dto.getTenantId());
        assertEquals(po.getProfitSharingMode(), dto.getProfitSharingMode());
        assertEquals(po.getProfitSharingSwitch(), dto.getProfitSharingSwitch());
        assertEquals(po.getCreateTime(), dto.getCreateTime());
        assertEquals(po.getUpdateTime(), dto.getUpdateTime());
        assertEquals(po.getCreatedBy(), dto.getCreatedBy());
        assertEquals(po.getUpdatedBy(), dto.getUpdatedBy());
    }

    @Test
    void testPoToDto_Null() {
        // When
        ProfitSharingConfigDTO dto = ProfitSharingConfigConverter.poToDto(null);

        // Then
        assertNull(dto);
    }

    @Test
    void testDtoToPo() {
        // Given
        ProfitSharingConfigDTO dto = new ProfitSharingConfigDTO();
        dto.setId(1L);
        dto.setTenantId(1001L);
        dto.setProfitSharingMode(2);
        dto.setProfitSharingSwitch(1);
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setCreatedBy("test");
        dto.setUpdatedBy("test");

        // When
        BillProfitSharingConfig po = ProfitSharingConfigConverter.dtoToPo(dto);

        // Then
        assertNotNull(po);
        assertEquals(dto.getId(), po.getId());
        assertEquals(dto.getTenantId(), po.getTenantId());
        assertEquals(dto.getProfitSharingMode(), po.getProfitSharingMode());
        assertEquals(dto.getProfitSharingSwitch(), po.getProfitSharingSwitch());
        assertEquals(dto.getCreateTime(), po.getCreateTime());
        assertEquals(dto.getUpdateTime(), po.getUpdateTime());
        assertEquals(dto.getCreatedBy(), po.getCreatedBy());
        assertEquals(dto.getUpdatedBy(), po.getUpdatedBy());
    }

    @Test
    void testDtoToPo_Null() {
        // When
        BillProfitSharingConfig po = ProfitSharingConfigConverter.dtoToPo(null);

        // Then
        assertNull(po);
    }

    @Test
    void testSaveVoToDto() {
        // Given
        ProfitSharingConfigSaveVO saveVO = new ProfitSharingConfigSaveVO();
        saveVO.setId(1L);
        saveVO.setTenantId(1001L);
        saveVO.setProfitSharingMode(2);
        saveVO.setProfitSharingSwitch(1);
        saveVO.setCreatedBy("test");
        saveVO.setUpdatedBy("test");

        // When
        ProfitSharingConfigDTO dto = ProfitSharingConfigConverter.saveVoToDto(saveVO);

        // Then
        assertNotNull(dto);
        assertEquals(saveVO.getId(), dto.getId());
        assertEquals(saveVO.getTenantId(), dto.getTenantId());
        assertEquals(saveVO.getProfitSharingMode(), dto.getProfitSharingMode());
        assertEquals(saveVO.getProfitSharingSwitch(), dto.getProfitSharingSwitch());
        assertEquals(saveVO.getCreatedBy(), dto.getCreatedBy());
        assertEquals(saveVO.getUpdatedBy(), dto.getUpdatedBy());
    }

    @Test
    void testSaveVoToDto_Null() {
        // When
        ProfitSharingConfigDTO dto = ProfitSharingConfigConverter.saveVoToDto(null);

        // Then
        assertNull(dto);
    }

    @Test
    void testCreateDefaultConfig() {
        // Given
        Long tenantId = 1001L;

        // When
        ProfitSharingConfigDTO dto = ProfitSharingConfigConverter.createDefaultConfig(tenantId);

        // Then
        assertNotNull(dto);
        assertEquals(tenantId, dto.getTenantId());
        assertEquals(1, dto.getProfitSharingMode()); // 默认延迟分账
        assertEquals(0, dto.getProfitSharingSwitch()); // 默认关闭
        assertNull(dto.getId());
        assertNull(dto.getCreateTime());
        assertNull(dto.getUpdateTime());
        assertNull(dto.getCreatedBy());
        assertNull(dto.getUpdatedBy());
    }
}
