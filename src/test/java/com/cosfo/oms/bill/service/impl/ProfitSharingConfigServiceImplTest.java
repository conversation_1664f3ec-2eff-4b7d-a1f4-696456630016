package com.cosfo.oms.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.oms.bill.dao.BillProfitSharingConfigDao;
import com.cosfo.oms.bill.model.dto.ProfitSharingConfigDTO;
import com.cosfo.oms.bill.model.po.BillProfitSharingConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 分账配置服务测试类
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@ExtendWith(MockitoExtension.class)
class ProfitSharingConfigServiceImplTest {

    @Mock
    private BillProfitSharingConfigDao billProfitSharingConfigDao;

    @InjectMocks
    private ProfitSharingConfigServiceImpl profitSharingConfigService;

    private Long testTenantId;
    private BillProfitSharingConfig testConfig;

    @BeforeEach
    void setUp() {
        testTenantId = 1001L;
        testConfig = new BillProfitSharingConfig();
        testConfig.setId(1L);
        testConfig.setTenantId(testTenantId);
        testConfig.setProfitSharingMode(2);
        testConfig.setProfitSharingSwitch(1);
        testConfig.setCreateTime(LocalDateTime.now());
        testConfig.setUpdateTime(LocalDateTime.now());
        testConfig.setCreatedBy("test");
        testConfig.setUpdatedBy("test");
    }

    @Test
    void testGetConfigByTenantId_ExistingConfig() {
        // Given
        when(billProfitSharingConfigDao.getOne(any(LambdaQueryWrapper.class))).thenReturn(testConfig);

        // When
        ProfitSharingConfigDTO result = profitSharingConfigService.getConfigByTenantId(testTenantId);

        // Then
        assertNotNull(result);
        assertEquals(testTenantId, result.getTenantId());
        assertEquals(2, result.getProfitSharingMode());
        assertEquals(1, result.getProfitSharingSwitch());
        assertEquals("test", result.getCreatedBy());
        assertEquals("test", result.getUpdatedBy());

        verify(billProfitSharingConfigDao, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetConfigByTenantId_NonExistingConfig() {
        // Given
        when(billProfitSharingConfigDao.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // When
        ProfitSharingConfigDTO result = profitSharingConfigService.getConfigByTenantId(testTenantId);

        // Then
        assertNotNull(result);
        assertEquals(testTenantId, result.getTenantId());
        assertEquals(1, result.getProfitSharingMode()); // 默认延迟分账
        assertEquals(0, result.getProfitSharingSwitch()); // 默认关闭
        assertNull(result.getId());

        verify(billProfitSharingConfigDao, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetConfigByTenantId_NullTenantId() {
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            profitSharingConfigService.getConfigByTenantId(null);
        });

        verify(billProfitSharingConfigDao, never()).getOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testSaveOrUpdateConfig_NewConfig() {
        // Given
        ProfitSharingConfigDTO configDTO = new ProfitSharingConfigDTO();
        configDTO.setTenantId(testTenantId);
        configDTO.setProfitSharingMode(2);
        configDTO.setProfitSharingSwitch(1);
        configDTO.setCreatedBy("testUser");
        configDTO.setUpdatedBy("testUser");

        when(billProfitSharingConfigDao.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(billProfitSharingConfigDao.saveOrUpdate(any(BillProfitSharingConfig.class))).thenReturn(true);

        // When
        ProfitSharingConfigDTO result = profitSharingConfigService.saveOrUpdateConfig(configDTO);

        // Then
        assertNotNull(result);
        assertEquals(testTenantId, result.getTenantId());
        assertEquals(2, result.getProfitSharingMode());
        assertEquals(1, result.getProfitSharingSwitch());
        assertNotNull(result.getCreateTime());
        assertNotNull(result.getUpdateTime());

        verify(billProfitSharingConfigDao, times(1)).getOne(any(LambdaQueryWrapper.class));
        verify(billProfitSharingConfigDao, times(1)).saveOrUpdate(any(BillProfitSharingConfig.class));
    }

    @Test
    void testSaveOrUpdateConfig_UpdateExistingConfig() {
        // Given
        ProfitSharingConfigDTO configDTO = new ProfitSharingConfigDTO();
        configDTO.setTenantId(testTenantId);
        configDTO.setProfitSharingMode(1);
        configDTO.setProfitSharingSwitch(0);
        configDTO.setUpdatedBy("updateUser");

        when(billProfitSharingConfigDao.getOne(any(LambdaQueryWrapper.class))).thenReturn(testConfig);
        when(billProfitSharingConfigDao.saveOrUpdate(any(BillProfitSharingConfig.class))).thenReturn(true);

        // When
        ProfitSharingConfigDTO result = profitSharingConfigService.saveOrUpdateConfig(configDTO);

        // Then
        assertNotNull(result);
        assertEquals(testTenantId, result.getTenantId());
        assertEquals(1, result.getProfitSharingMode());
        assertEquals(0, result.getProfitSharingSwitch());
        assertEquals("updateUser", result.getUpdatedBy());

        verify(billProfitSharingConfigDao, times(1)).getOne(any(LambdaQueryWrapper.class));
        verify(billProfitSharingConfigDao, times(1)).saveOrUpdate(any(BillProfitSharingConfig.class));
    }

    @Test
    void testSaveOrUpdateConfig_NullConfigDTO() {
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            profitSharingConfigService.saveOrUpdateConfig(null);
        });

        verify(billProfitSharingConfigDao, never()).getOne(any(LambdaQueryWrapper.class));
        verify(billProfitSharingConfigDao, never()).saveOrUpdate(any(BillProfitSharingConfig.class));
    }

    @Test
    void testSaveOrUpdateConfig_NullTenantId() {
        // Given
        ProfitSharingConfigDTO configDTO = new ProfitSharingConfigDTO();
        configDTO.setTenantId(null);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            profitSharingConfigService.saveOrUpdateConfig(configDTO);
        });

        verify(billProfitSharingConfigDao, never()).getOne(any(LambdaQueryWrapper.class));
        verify(billProfitSharingConfigDao, never()).saveOrUpdate(any(BillProfitSharingConfig.class));
    }

    @Test
    void testSaveOrUpdateConfig_SaveFailed() {
        // Given
        ProfitSharingConfigDTO configDTO = new ProfitSharingConfigDTO();
        configDTO.setTenantId(testTenantId);
        configDTO.setProfitSharingMode(2);
        configDTO.setProfitSharingSwitch(1);

        when(billProfitSharingConfigDao.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(billProfitSharingConfigDao.saveOrUpdate(any(BillProfitSharingConfig.class))).thenReturn(false);

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            profitSharingConfigService.saveOrUpdateConfig(configDTO);
        });

        verify(billProfitSharingConfigDao, times(1)).getOne(any(LambdaQueryWrapper.class));
        verify(billProfitSharingConfigDao, times(1)).saveOrUpdate(any(BillProfitSharingConfig.class));
    }
}
