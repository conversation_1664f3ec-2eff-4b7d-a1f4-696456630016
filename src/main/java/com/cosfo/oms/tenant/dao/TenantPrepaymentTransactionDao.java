package com.cosfo.oms.tenant.dao;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.oms.bill.model.dto.PrepaymentTransactionQueryDTO;
import com.cosfo.oms.bill.model.vo.PrepaymentTransactionTotalVO;
import com.cosfo.oms.tenant.model.po.TenantPrepaymentTransaction;


import java.util.List;

/**
 * <p>
 * 预付交易明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface TenantPrepaymentTransactionDao extends IService<TenantPrepaymentTransaction> {

    /**
     * 获取分页
     * @param queryDTO
     * @return
     */
    Page<TenantPrepaymentTransaction> queryPrepaymentTransactionPage(PrepaymentTransactionQueryDTO queryDTO);

    /**
     * 获取列表
     * @param queryDTO
     * @return
     */
    List<TenantPrepaymentTransaction> queryPrepaymentTransactionList(PrepaymentTransactionQueryDTO queryDTO);

    /**
     * 获取合计
     * @param queryDTO
     * @return
     */
    PrepaymentTransactionTotalVO queryPrepaymentTransactionTotal(PrepaymentTransactionQueryDTO queryDTO);
}
