package com.cosfo.oms.msgscene.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息场景-租户映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MsgSceneTenantMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 场景id
     */
    private Long sceneId;

    /**
     * 模版id
     */
    private Long templateId;

    /**
     * 三方模版id
     */
    private String thirdTemplateId;

    /**
     * 模版类型1=微信
     */
    private Integer templateType;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 推送权限0=禁用;1=启用
     */
    private Integer availableStatus;

    /**
     * 用户id
     */
    private Long uId;

    /**
     * 用户名称
     */
    private String uName;


}
