package com.cosfo.oms.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date : 2022/12/21 14:48
 */
@Configuration
@Data
public class HuiFuConfig {
    @Value("${huifu.sys_id}")
    private String sysId;
    @Value("${huifu.product_id}")
    private String productId;
    @Value("${huifu.channel_no}")
    private String channelNo;
    @Value("${huifu.private_key}")
    private String privateKey;

}
