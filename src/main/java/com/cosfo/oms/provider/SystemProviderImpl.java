package com.cosfo.oms.provider;

import com.cosfo.oms.client.provider.system.SystemParameterQueryProvider;
import com.cosfo.oms.client.resp.SystemParameterResultResp;
import com.cosfo.oms.system.mapper.SystemParametersMapper;
import com.cosfo.oms.system.model.po.SystemParameters;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc
 * @date 2022/12/30 10:47
 */
@DubboService
public class SystemProviderImpl implements SystemParameterQueryProvider {
    @Resource
    private SystemParametersMapper systemParametersMapper;


    @Override
    public DubboResponse<SystemParameterResultResp> selectByKey(String key) {
        SystemParameters systemParameters = systemParametersMapper.selectByKey(key);
        SystemParameterResultResp systemParameterResultResp = new SystemParameterResultResp();
        systemParameterResultResp.setParamKey(systemParameters.getParamKey());
        systemParameterResultResp.setParamValue(systemParameters.getParamValue());
        return DubboResponse.getOK(systemParameterResultResp);
    }
}
