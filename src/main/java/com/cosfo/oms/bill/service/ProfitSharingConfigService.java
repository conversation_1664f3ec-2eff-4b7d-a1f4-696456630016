package com.cosfo.oms.bill.service;

import com.cosfo.oms.bill.model.dto.ProfitSharingConfigDTO;

/**
 * 分账配置服务接口
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
public interface ProfitSharingConfigService {

    /**
     * 根据租户ID查询分账配置
     * 如果不存在则返回默认配置
     *
     * @param tenantId 租户ID
     * @return 分账配置DTO
     */
    ProfitSharingConfigDTO getConfigByTenantId(Long tenantId);

    /**
     * 保存或更新分账配置
     *
     * @param configDTO 分账配置DTO
     * @return 保存后的配置DTO
     */
    ProfitSharingConfigDTO saveOrUpdateConfig(ProfitSharingConfigDTO configDTO);
}
