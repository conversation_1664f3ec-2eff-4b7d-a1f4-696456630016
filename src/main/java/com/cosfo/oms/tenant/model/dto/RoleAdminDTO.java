package com.cosfo.oms.tenant.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/3/29 14:07
 * @Description: 默认管理员参数
 */
@Data
public class RoleAdminDTO {

    /**
     * 角色名
     */
    private String rolename;
    /**
     * 备注
     */
    private String remarks;
    private Date createTime;
    private Date updateTime;
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 系统来源
     * @see net.xianmu.authentication.client.input.SystemOriginEnum
     */
    private Integer systemOrigin;

    /**
     * 0默认，非超级管理员  超级管理员1
     */
    private Byte superAdmin;

    /**
     * 菜单ids
     */
    private List<Long> menuPurviewIds;
}
