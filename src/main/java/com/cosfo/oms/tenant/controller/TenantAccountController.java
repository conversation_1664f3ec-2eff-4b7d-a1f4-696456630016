package com.cosfo.oms.tenant.controller;

import com.cosfo.oms.common.constant.AuthPermissionConstant;
import com.cosfo.oms.controller.BaseController;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.tenant.model.dto.*;
import com.cosfo.oms.tenant.model.vo.SuperAccountLoginVO;
import com.cosfo.oms.tenant.model.vo.TenantAccountLoginVO;
import com.cosfo.oms.tenant.model.vo.TenantAccountVO;
import com.cosfo.oms.tenant.service.TenantAccountService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 登录模块
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@RestController
@RequestMapping("/tenant/user")
public class TenantAccountController extends BaseController {

    @Resource
    private TenantAccountService tenantAccountService;

    /**
     * 登录(已废弃)
     *
     * @param tenantAccountLoginDTO
     * @return
     */
    @Deprecated
    @PostMapping("query/login")
    public CommonResult<TenantAccountLoginVO> loginOld(@Valid @RequestBody TenantAccountLoginDTO tenantAccountLoginDTO){
        TenantAccountLoginVO loginVO = tenantAccountService.loginOld(tenantAccountLoginDTO);
        return CommonResult.ok(loginVO);
    }

    /**
     * 登录V2
     *
     * @param tenantAccountLoginDTO
     * @return
     */
    @PostMapping("query/loginV2")
    public CommonResult<TenantAccountLoginVO> loginV2(@Valid @RequestBody TenantAccountLoginDTO tenantAccountLoginDTO){
        TenantAccountLoginVO loginVO = tenantAccountService.loginV2(tenantAccountLoginDTO);
        return CommonResult.ok(loginVO);
    }

    /**
     * 更改用户信息
     *
     * @param tenantAccountDTO
     * @return
     */
    @PostMapping("upsert/user/info")
    public CommonResult<Boolean> updateUserInfo(@RequestBody TenantAccountDTO tenantAccountDTO){
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(tenantAccountService.updateUserInfo(tenantAccountDTO, merchantInfoDTO));
    }

    /**
     * 新增手机号
     *
     * @param tenantAccountDTO
     * @return
     */
    @PostMapping("/upsert/add")
    public CommonResult<Boolean> add(@Valid @RequestBody TenantAccountDTO tenantAccountDTO){
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        tenantAccountDTO.setTenantId(merchantInfoDTO.getTenantId());
        tenantAccountDTO.setOperatorPhone(merchantInfoDTO.getPhone());
        tenantAccountDTO.setUpdater(merchantInfoDTO.getUserName());
        tenantAccountDTO.setSystemOrigin(SystemOriginEnum.COSFO_OMS.getType());
        return CommonResult.ok(tenantAccountService.create(tenantAccountDTO));
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    @PostMapping("query/login/user-info")
    public CommonResult<TenantAccountVO> getLoginUserInfo(){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        TenantAccountVO tenantAccountVO = tenantAccountService.getTenantAccountVO(loginContextInfoDTO.getAuthUserId());
        return CommonResult.ok(tenantAccountVO);
    }

    /**
     * 账号列表
     *
     * @param tenantAccountListQueryDTO
     * @return
     */
    @PostMapping("query/account/list")
    public CommonResult<PageInfo<TenantAccountVO>> accountList(@RequestBody TenantAccountListQueryDTO tenantAccountListQueryDTO){
        PageInfo<TenantAccountVO> pageInfo = tenantAccountService.page(tenantAccountListQueryDTO, getMerchantInfoDTO());
        return CommonResult.ok(pageInfo);
    }

    /**
     * 删除用户
     *
     * @param id
     * @return
     */
    @PostMapping("upsert/delete")
    public CommonResult<Boolean> deleteUser(Long id){
        tenantAccountService.remove(id, getMerchantInfoDTO());
        return CommonResult.ok(Boolean.TRUE);
    }

    /**
     * 发送验证码
     * @param sendCodeDTO
     * @return
     */
    @PostMapping(value = "/sendCode")
    public CommonResult<Boolean> sendCode(@Valid @RequestBody SendCodeDTO sendCodeDTO) {
        return CommonResult.ok(tenantAccountService.sendCode(sendCodeDTO.getPhone()));
    }

    /**
     * 校验验证码
     * @param sendCodeDTO
     * @return
     */
    @PostMapping(value = "/examineCode")
    public CommonResult<Boolean> examineCode(@RequestBody SendCodeDTO sendCodeDTO) {
        Boolean result = tenantAccountService.examineCode(sendCodeDTO);
        return CommonResult.ok(result);
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    @PostMapping("query/user-info")
    public CommonResult<TenantAccountVO> getUserInfo(Long accountId){
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        TenantAccountVO tenantAccountVO = tenantAccountService.queryAccountInfo(merchantInfoDTO.getTenantId(), accountId);
        return CommonResult.ok(tenantAccountVO);
    }

    /**
     * 切换超级账号
     * <p>通过oms token 交换目标租户超级账号token</p>
     * @param superAccountLoginDTO 目标租户信息
     * @return 超级账号token
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_READ_ROLE_CODE})
    @PostMapping("query/switch-super-account")
    public CommonResult<SuperAccountLoginVO> switchSuperAccount(@RequestBody SuperAccountLoginDTO superAccountLoginDTO){
        String superAccountToken = tenantAccountService.loginSuperAccount(superAccountLoginDTO.getTargetTenantId(), getMerchantInfoDTO().getAuthUserId());
        SuperAccountLoginVO superAccountLoginVO = new SuperAccountLoginVO();
        superAccountLoginVO.setSuperAccountToken(superAccountToken);
        return CommonResult.ok(superAccountLoginVO);
    }
}
