<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.oms.product.mapper.ProductPricingSupplyMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.oms.product.model.po.ProductPricingSupply">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="supply_sku_id" jdbcType="BIGINT" property="supplySkuId"/>
        <result column="supply_tenant_id" jdbcType="BIGINT" property="supplyTenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , tenant_id, sku_id, supply_sku_id, supply_tenant_id, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from product_pricing_supply
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByPrimaryKeys" resultType="com.cosfo.oms.product.model.po.ProductPricingSupply">
        select
        <include refid="Base_Column_List"/>
        from product_pricing_supply
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from product_pricing_supply
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByPrimaryKeys">
        delete
        from product_pricing_supply
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.oms.product.model.po.ProductPricingSupply" useGeneratedKeys="true">
        insert into product_pricing_supply (tenant_id, sku_id, supply_sku_id,
        supply_tenant_id, start_time, end_time,
        create_time, update_time)
        values (#{tenantId,jdbcType=BIGINT}, #{skuId,jdbcType=BIGINT}, #{supplySkuId,jdbcType=BIGINT},
        #{supplyTenantId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.oms.product.model.po.ProductPricingSupply" useGeneratedKeys="true">
        insert into product_pricing_supply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="skuId != null">
                sku_id,
            </if>
            <if test="supplySkuId != null">
                supply_sku_id,
            </if>
            <if test="supplyTenantId != null">
                supply_tenant_id,
            </if>
            <if test="associated != null">
                associated,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=BIGINT},
            </if>
            <if test="supplySkuId != null">
                #{supplySkuId,jdbcType=BIGINT},
            </if>
            <if test="supplyTenantId != null">
                #{supplyTenantId,jdbcType=BIGINT},
            </if>
            <if test="associated != null">
                #{associated,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.oms.product.model.po.ProductPricingSupply">
        update product_pricing_supply
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId,jdbcType=BIGINT},
            </if>
            <if test="supplySkuId != null">
                supply_sku_id = #{supplySkuId,jdbcType=BIGINT},
            </if>
            <if test="supplyTenantId != null">
                supply_tenant_id = #{supplyTenantId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.oms.product.model.po.ProductPricingSupply">
        update product_pricing_supply
        set tenant_id = #{tenantId,jdbcType=BIGINT},
        sku_id = #{skuId,jdbcType=BIGINT},
        supply_sku_id = #{supplySkuId,jdbcType=BIGINT},
        supply_tenant_id = #{supplyTenantId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="list" resultType="com.cosfo.oms.product.model.vo.ProductPricingSupplyVO">
        select
        p.tenant_id tenantId, p.id, city.id citySupplyPriceId, p.supply_sku_id supplySkuId, p.supply_tenant_id supplyTenantId, city.price, city.type,
        t.tenant_name tenantName,
        case
        when city.end_time &lt; now() Then 1
        when city.start_time &gt; now() Then 2
        when city.start_time &lt;= now() and city.end_time &gt;= now() Then 0 end as expireStatus, if(associated = 0,
        1, 0) as usingStatus, city.start_time startTime, city.end_time endTime,city.city_id cityId,strategy_value strategyValue
        from product_pricing_supply p
        left join product_pricing_supply_city_mapping city on city.product_pricing_supply_id = p.id
        left join tenant t on p.tenant_id = t.id
        <where>
            city.deleted = 0 and p.supply_sku_id is not null
            <if test="productPricingSupplyQueryDTO.tenantName != null">
                and t.tenant_name like #{productPricingSupplyQueryDTO.tenantName}
            </if>
            <if test="productPricingSupplyQueryDTO.skuId != null">
                and p.supply_sku_id = #{productPricingSupplyQueryDTO.skuId}
            </if>
            <if test="productPricingSupplyQueryDTO.supplySkuIds != null and productPricingSupplyQueryDTO.supplySkuIds.size() > 0">
                and p.supply_sku_id in
                <foreach collection="productPricingSupplyQueryDTO.supplySkuIds" open="(" close=")" separator=","
                         item="item">
                    #{item}
                </foreach>
            </if>
            <if test="productPricingSupplyQueryDTO.cityIds != null and productPricingSupplyQueryDTO.cityIds.size() > 0">
                and city.city_id in
                <foreach collection="productPricingSupplyQueryDTO.cityIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="productPricingSupplyQueryDTO.tenantId != null">
                and p.tenant_id = #{productPricingSupplyQueryDTO.tenantId}
            </if>
            <if test="productPricingSupplyQueryDTO.expireStatus != null">
                <choose>
                    <when test="productPricingSupplyQueryDTO.expireStatus == 0">
                        and city.start_time &lt;= now() and city.end_time &gt;= now()
                    </when>
                    <when test="productPricingSupplyQueryDTO.expireStatus == 1">
                        and city.end_time &lt;= now()
                    </when>
                    <when test="productPricingSupplyQueryDTO.expireStatus == 2">
                        and city.start_time &gt;= now()
                    </when>
                </choose>
            </if>
            <if test="productPricingSupplyQueryDTO.usingStatus != null">
                <choose>
                    <when test="productPricingSupplyQueryDTO.usingStatus == 0">
                        and p.associated =1
                    </when>
                    <when test="productPricingSupplyQueryDTO.usingStatus == 1">
                        and p.associated =0
                    </when>
                </choose>
            </if>
            <if test="productPricingSupplyQueryDTO.citySupplyPriceIds != null and productPricingSupplyQueryDTO.citySupplyPriceIds.size() > 0">
                and city.id in
                <foreach collection="productPricingSupplyQueryDTO.citySupplyPriceIds" open="(" close=")" separator=","
                         item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        order by city.id desc
    </select>

    <select id="exportList" resultType="com.cosfo.oms.product.model.vo.ProductPricingSupplyVO" fetchSize="1000" resultSetType="FORWARD_ONLY">
        select
        p.tenant_id tenantId, p.id, city.id citySupplyPriceId, p.supply_sku_id supplySkuId,p.supply_tenant_id supplyTenantId, city.price, city.type,city.strategy_value strategyValue,
        t.tenant_name tenantName, if(city.start_time &lt;= now() and
        city.end_time &gt;= now(),0,1) expireStatus, if(p.sku_id is null , 1, 0) as usingStatus, if(p.associated = 0 ,'未采用', '采用中') as usingStatusStr, city.start_time startTime, city.end_time endTime,city.city_id cityId
        from product_pricing_supply p
        left join product_pricing_supply_city_mapping city on city.product_pricing_supply_id = p.id
        left join tenant t on p.tenant_id = t.id
        <where>
            city.deleted = 0 and p.supply_sku_id is not null
            <if test="productPricingSupplyExportDTO.tenantName != null">
                and t.tenant_name like #{productPricingSupplyExportDTO.tenantName}
            </if>
            <if test="productPricingSupplyExportDTO.skuId != null">
                and p.supply_sku_id = #{productPricingSupplyExportDTO.skuId}
            </if>
            <if test="productPricingSupplyExportDTO.supplySkuIds != null and productPricingSupplyExportDTO.supplySkuIds.size() > 0">
                and p.supply_sku_id in
                <foreach collection="productPricingSupplyExportDTO.supplySkuIds" open="(" close=")" separator=","
                         item="item">
                    #{item}
                </foreach>
            </if>
            <if test="productPricingSupplyExportDTO.cityIds != null and productPricingSupplyExportDTO.cityIds.size() > 0">
                and city.city_id in
                <foreach collection="productPricingSupplyExportDTO.cityIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="productPricingSupplyExportDTO.tenantId != null">
                and p.tenant_id = #{productPricingSupplyExportDTO.tenantId}
            </if>
            <if test="productPricingSupplyExportDTO.expireStatus != null">
                <choose>
                    <when test="productPricingSupplyExportDTO.expireStatus == 0">
                        and city.start_time &lt;= now() and city.end_time &gt;= now()
                    </when>
                    <when test="productPricingSupplyExportDTO.expireStatus == 1">
                        and city.end_time &lt;= now()
                    </when>
                    <when test="productPricingSupplyExportDTO.expireStatus == 2">
                        and city.start_time &gt;= now()
                    </when>
                </choose>
            </if>
            <if test="productPricingSupplyExportDTO.usingStatus != null">
                <choose>
                    <when test="productPricingSupplyExportDTO.usingStatus == 0">
                        and p.associated =1
                    </when>
                    <when test="productPricingSupplyExportDTO.usingStatus == 1">
                        and p.associated =0
                    </when>
                </choose>
            </if>
        </where>
        order by city.id desc
    </select>


    <select id="selectByTenantIdAndSupplyIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from
        product_pricing_supply
        where
        tenant_id = #{tenantId}
        and supply_sku_id in
        <foreach collection="supplySkuIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectByTenantId" resultMap="BaseResultMap">
        select
        distinct(supply_sku_id)
        from
        product_pricing_supply
        <where>
            <if test="tenantId != null">
                tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <select id="selectById" resultType="com.cosfo.oms.product.model.vo.ProductPricingSupplyVO">
        select
        p.tenant_id tenantId, p.id, city.id citySupplyPriceId, p.supply_sku_id supplySkuId, city.price, city.type,
        if(city.start_time &lt;= now() and city.end_time &gt;= now(),0,1) expireStatus, if(p.sku_id is null , 1, 0) as
        usingStatus,
        city.start_time startTime, city.end_time endTime,city.city_id cityId
        from product_pricing_supply p
        left join product_pricing_supply_city_mapping city on city.product_pricing_supply_id = p.id
        where city.deleted = 0
        and city.id = #{citySupplyPriceId}
    </select>

    <select id="selectByTenantIdAndSupplySkuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from
        product_pricing_supply
        where
        tenant_id = #{tenantId}
        and supply_sku_id = #{supplySkuId}
    </select>

</mapper>