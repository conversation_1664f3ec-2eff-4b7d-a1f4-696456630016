package com.cosfo.oms.facade.dto.tenant;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TenantAccountInputDTO implements Serializable {
    /**
     * id
     */
    private Long nickId;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 角色Id
     */
    private Long roleId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * authUserIds
     */
    private List<Long> authUserIds;

    private Integer pageSize = 10;
    private Integer pageIndex = 1;
}
