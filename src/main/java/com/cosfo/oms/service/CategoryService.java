package com.cosfo.oms.service;

import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.model.dto.CategoryDTO;
import com.cosfo.oms.model.vo.CategoryVO;

import java.util.List;
import java.util.Set;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/8
 */
public interface CategoryService {

    /**
     * 新增类目
     *
     * @param categoryDTO
     * @return
     */
    ResultDTO add(CategoryDTO categoryDTO);

    /**
     * 编辑类目
     *
     * @param categoryDTO
     * @return
     */
    ResultDTO edit(CategoryDTO categoryDTO);

    /**
     * 删除类目
     *
     * @param id
     * @return
     */
    ResultDTO remove(Long id);

    /**
     * 类目列表
     *
     * @return
     */
    ResultDTO list(String name, Integer type);

    /**
     * 获取类目树
     *
     * @param categoryIds
     * @return
     */
    List<CategoryVO> getCategoryTree(List<CategoryVO> treeNodes, List<Long> categoryIds, Integer type);

}

