package com.cosfo.oms.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.oms.bill.converter.ProfitSharingConfigConverter;
import com.cosfo.oms.bill.dao.BillProfitSharingConfigDao;
import com.cosfo.oms.bill.model.dto.ProfitSharingConfigDTO;
import com.cosfo.oms.bill.model.po.BillProfitSharingConfig;
import com.cosfo.oms.bill.service.ProfitSharingConfigService;
import com.cosfo.oms.common.result.ResultDTOEnum;
import com.cosfo.oms.common.utils.AssertParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 分账配置服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Slf4j
@Service
public class ProfitSharingConfigServiceImpl implements ProfitSharingConfigService {

    @Resource
    private BillProfitSharingConfigDao billProfitSharingConfigDao;

    @Override
    public ProfitSharingConfigDTO getConfigByTenantId(Long tenantId) {
        log.info("查询分账配置，tenantId: {}", tenantId);
        
        AssertParam.notNull(tenantId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户ID不能为空");

        // 查询数据库中的配置
        LambdaQueryWrapper<BillProfitSharingConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillProfitSharingConfig::getTenantId, tenantId);
        BillProfitSharingConfig config = billProfitSharingConfigDao.getOne(queryWrapper);

        ProfitSharingConfigDTO result;
        if (config != null) {
            // 存在配置，转换为DTO返回
            result = ProfitSharingConfigConverter.poToDto(config);
            log.info("查询到分账配置，tenantId: {}, config: {}", tenantId, result);
        } else {
            // 不存在配置，返回默认配置
            result = ProfitSharingConfigConverter.createDefaultConfig(tenantId);
            log.info("未查询到分账配置，返回默认配置，tenantId: {}, defaultConfig: {}", tenantId, result);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProfitSharingConfigDTO saveOrUpdateConfig(ProfitSharingConfigDTO configDTO) {
        log.info("保存或更新分账配置，configDTO: {}", configDTO);
        
        AssertParam.notNull(configDTO, ResultDTOEnum.PARAMETER_MISSING.getCode(), "分账配置不能为空");
        AssertParam.notNull(configDTO.getTenantId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户ID不能为空");

        try {
            // 查询是否已存在配置
            LambdaQueryWrapper<BillProfitSharingConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BillProfitSharingConfig::getTenantId, configDTO.getTenantId());
            BillProfitSharingConfig existingConfig = billProfitSharingConfigDao.getOne(queryWrapper);

            BillProfitSharingConfig configToSave;
            if (existingConfig != null) {
                // 更新现有配置
                configToSave = existingConfig;
                configToSave.setProfitSharingMode(configDTO.getProfitSharingMode());
                configToSave.setProfitSharingSwitch(configDTO.getProfitSharingSwitch());
                configToSave.setUpdateTime(LocalDateTime.now());
                configToSave.setUpdatedBy(configDTO.getUpdatedBy());
                
                log.info("更新分账配置，tenantId: {}", configDTO.getTenantId());
            } else {
                // 新增配置
                configToSave = ProfitSharingConfigConverter.dtoToPo(configDTO);
                configToSave.setCreateTime(LocalDateTime.now());
                configToSave.setUpdateTime(LocalDateTime.now());
                
                log.info("新增分账配置，tenantId: {}", configDTO.getTenantId());
            }

            // 保存到数据库
            boolean saveResult = billProfitSharingConfigDao.saveOrUpdate(configToSave);
            if (!saveResult) {
                log.error("保存分账配置失败，tenantId: {}", configDTO.getTenantId());
                throw new RuntimeException("保存分账配置失败");
            }

            // 返回保存后的配置
            ProfitSharingConfigDTO result = ProfitSharingConfigConverter.poToDto(configToSave);
            log.info("保存分账配置成功，tenantId: {}, result: {}", configDTO.getTenantId(), result);
            
            return result;
            
        } catch (Exception e) {
            log.error("保存分账配置异常，tenantId: {}, error: {}", configDTO.getTenantId(), e.getMessage(), e);
            throw e;
        }
    }
}
