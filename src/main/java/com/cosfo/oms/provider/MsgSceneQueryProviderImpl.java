package com.cosfo.oms.provider;

import com.cosfo.oms.client.provider.msgscene.MsgSceneQueryProvider;
import com.cosfo.oms.client.req.MsgSceneQueryReq;
import com.cosfo.oms.client.req.ThirdTemplateIdQueryReq;
import com.cosfo.oms.client.resp.MsgSceneResultResp;
import com.cosfo.oms.client.resp.MsgSceneTenantResultResp;
import com.cosfo.oms.msgscene.convert.MsgSceneConverter;
import com.cosfo.oms.msgscene.dto.MsgSceneQueryDTO;
import com.cosfo.oms.msgscene.service.MsgSceneDomainService;
import com.cosfo.oms.msgscene.vo.MsgSceneTenantMappingVO;
import com.cosfo.oms.msgscene.vo.TemplateIdQueryVO;
import com.cosfo.oms.wechat.model.dto.MsgSceneDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/2/22 19:59
 */
@DubboService
public class MsgSceneQueryProviderImpl implements MsgSceneQueryProvider {
    @Resource
    private MsgSceneDomainService msgSceneDomainService;


    @Override
    public DubboResponse<String> getTemplateId(ThirdTemplateIdQueryReq wechatTemplateIdQueryReq) {
        TemplateIdQueryVO templateIdQueryVO = MsgSceneConverter.templateIdQueryReq2VO(wechatTemplateIdQueryReq);
        return DubboResponse.getOK(msgSceneDomainService.getThirdTemplateId(templateIdQueryVO));
    }

    @Override
    public DubboResponse<List<MsgSceneTenantResultResp>> listSceneTenantByTenantId(MsgSceneQueryReq msgSceneQueryReq,Long tenantId) {
        MsgSceneQueryDTO queryDTO = MsgSceneConverter.msgSceneQueryReqReq2DTO(msgSceneQueryReq);
        List<MsgSceneTenantMappingVO> vos = msgSceneDomainService.listSceneTenantByTenantId(queryDTO,tenantId);
        return DubboResponse.getOK(vos.stream().map(e->MsgSceneConverter.msgSceneTenantVO2Resp(e)).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<MsgSceneResultResp> getScene(Long sceneId) {
        MsgSceneDTO scene = msgSceneDomainService.getWechatMsgSceneDetail(sceneId);
        return DubboResponse.getOK(MsgSceneConverter.scene2Resp(scene));
    }
}
