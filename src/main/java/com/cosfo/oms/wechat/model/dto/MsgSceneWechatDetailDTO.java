package com.cosfo.oms.wechat.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 微信消息模版关联场景明细DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
@Data
public class MsgSceneWechatDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key（帆台模板id）
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 微信模版名称(模板标题)
     */
    private String wechatTitle;

    /**
     * 场景说明
     */
    private String scene;

    /**
     * 场景总数
     */
    private Integer sceneCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 场景列表
     */
    private List<MsgSceneDTO> msgSceneList;

    /**
     * 关键词列表
     */
    private List<KeyWordDTO> keywords;

}
