package com.cosfo.oms.product.model.dto;

import com.cosfo.oms.model.dto.PageDTO;
import lombok.Data;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/8
 */
@Data
public class ProductPricingSupplyQueryDTO extends PageDTO {
    /**
     * 品牌方租户Id
     */
    private Long tenantId;
    /**
     * 报价对象商城名称
     */
    private String tenantName;
    /**
     * sku编码
     */
    private String sku;
    /**
     * skuID
     */
    private Long skuId;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 城市Id
     */
    private List<Long> cityIds;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 供应商sku编码
     */
    private List<Long> supplySkuIds;
    /**
     * 类目集合
     */
    private List<Long> categoryIds;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 报价状态 0生效中1失效中
     */
    private Integer expireStatus;

    /**
     * 采用状态 0采用中1未采用
     */
    private Integer usingStatus;
    /**
     * 供应状态 0在售 1未在售
     */
    private Integer supplyStatus;
    /**
     * 商品类型
     * 1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销
     */
    private Integer subAgentType;

    /**
     * 转换用，tenantName -> tenantIds
     */
    private List<Long> tenantIds;

    /**
     * 是否需要全路径类目
     */
    private Boolean isNeedAllPathCategory;

    /**
     * ids
     */
    private List<Long> citySupplyPriceIds;
}
