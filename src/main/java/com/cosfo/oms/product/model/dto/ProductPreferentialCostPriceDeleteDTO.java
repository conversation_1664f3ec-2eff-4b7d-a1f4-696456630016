package com.cosfo.oms.product.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: fansongsong
 * @Date: 2024-02-22
 * @Description:
 */
@Data
public class ProductPreferentialCostPriceDeleteDTO {

    /**
     * 租户ID
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;
    /**
     * skuid
     */
    @NotNull(message = "货品id不能为空")
    private Long skuId;
}
