package com.cosfo.oms.common.dingtalk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.io.IOException;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

@Slf4j
public class DingtalkApi {

    private static final String API_URL = "https://oapi.dingtalk.com/robot/send?access_token=";

    final static CloseableHttpClient HTTP_CLIENT = HttpClients.createDefault();

    public static boolean sendTextMessage(String accessToken, String content) throws IOException {

        // Build JSON message
        String jsonMessage = "{\"msgtype\": \"text\",\"text\": {\"content\":\"" + content + "\"}}";

        // Create HTTP client to send request

        // Create POST request with JSON data as entity
        HttpPost httpPost = new HttpPost(API_URL + accessToken);
        HttpEntity entity = new StringEntity(jsonMessage, ContentType.APPLICATION_JSON);
        httpPost.setEntity(entity);

        // Execute request and read response
        try (CloseableHttpResponse response = HTTP_CLIENT.execute(httpPost)) {
            log.info("Response status code: " + response.getStatusLine().getStatusCode());
            String json = IOUtils.toString(response.getEntity().getContent());
            if (HttpServletResponse.SC_OK != response.getStatusLine().getStatusCode()) {
                log.error("send dingtalk message error:{}", json);
                return false;
            } else {
                JSONObject jsonObject = JSON.parseObject(json);
                return jsonObject != null && 0 == jsonObject.getInteger("errcode");
            }
        }
    }

    public static void main(String[] args) throws IOException {
        sendTextMessage("947d58cfb53e4ba654c28fb54273fff171067912f4213f2954f58e3df332067f",
          "我就是一个审核打工仔" + new Date());
    }
}
