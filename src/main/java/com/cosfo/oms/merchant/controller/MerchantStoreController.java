package com.cosfo.oms.merchant.controller;

import com.cosfo.oms.common.config.ImportExcelUrlConfig;
import com.cosfo.oms.common.constant.AuthPermissionConstant;
import com.cosfo.oms.controller.BaseController;
import com.cosfo.oms.facade.DeliveryFenceQueryFacade;
import com.cosfo.oms.facade.WarehouseLogisticsQueryFacade;
import com.cosfo.oms.merchant.model.dto.ExcelImportResDTO;
import com.cosfo.oms.merchant.model.dto.MerchantStoreCreateDTO;
import com.cosfo.oms.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.oms.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.oms.merchant.model.dto.MerchantStoreUpdateDTO;
import com.cosfo.oms.merchant.model.dto.WarehouseQueryDTO;
import com.cosfo.oms.merchant.model.vo.ExpressCityWarehouseVO;
import com.cosfo.oms.merchant.model.vo.MerchantStoreDetailVO;
import com.cosfo.oms.merchant.model.vo.MerchantStoreVO;
import com.cosfo.oms.merchant.service.MerchantStoreService;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 品牌门店功能1
 *
 * <AUTHOR>
 * @description
 * @date 2022/5/21 11:34
 */
@RestController
@RequestMapping("/merchant/store")
public class MerchantStoreController extends BaseController {

    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private WarehouseLogisticsQueryFacade warehouseLogisticsQueryFacade;
    @Resource
    private DeliveryFenceQueryFacade deliveryFenceQueryFacade;

    /**
     * 门店列表
     *
     * @param merchantStoreDTO
     * @return
     */
    @RequestMapping(value = "/listAll", method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantStoreVO>> listAll(@Valid @RequestBody MerchantStoreDTO merchantStoreDTO) {
        return CommonResult.ok(merchantStoreService.listAll(merchantStoreDTO));
    }

    /**
     * 门店详情
     *
     * @param merchantStoreQueryDTO
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public CommonResult<MerchantStoreDetailVO> selectDetail(@RequestBody MerchantStoreQueryDTO merchantStoreQueryDTO) {
        return merchantStoreService.selectDetail(merchantStoreQueryDTO.getStoreId());
    }

    /**
     * 新建门店
     * @return
     */

    @PostMapping(value = "/save")
    public CommonResult<Long> save(@RequestBody MerchantStoreCreateDTO merchantStoreCreateDTO) {
        return merchantStoreService.createStore(merchantStoreCreateDTO);
    }


    /**
     * 更新/审核门店
     *
     * @param merchantStoreUpdateDTO
     * @return
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<Void> updateStore(@RequestBody MerchantStoreUpdateDTO merchantStoreUpdateDTO) {
        return merchantStoreService.updateStore(merchantStoreUpdateDTO);
    }

    /**
     * 导入更新门店履约excel
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/upsert/importBatchFulfillment")
    public CommonResult<ExcelImportResDTO> importBatchFulfillment(MultipartFile file) {
        return CommonResult.ok(merchantStoreService.importBatchFulfillment(file));
    }

    /**
     * 门店导出
     *
     * @return
     */
    @PostMapping(value = "/export")
    public void export(@Valid @RequestBody MerchantStoreDTO merchantStoreDTO) {
        merchantStoreService.export(merchantStoreDTO);
    }


    /**
     * 门店导入模板下载
     *
     * @return
     */
    @GetMapping(value = "/export-template")
    public CommonResult<String> exportTemplate() {
        return CommonResult.ok("");
    }


    /**
     * 门店可选择快递城配仓列表
     *
     * @return
     */
    @PostMapping(value = "/express-warehouse-list")
    public CommonResult<List<ExpressCityWarehouseVO>> expressWarehouseList(@RequestBody WarehouseQueryDTO warehouseQueryDTO) {
        Integer fulfillmentType = warehouseQueryDTO.getFulfillmentType();
        Integer status = warehouseQueryDTO.getStatus();
        return CommonResult.ok(warehouseLogisticsQueryFacade.expressWarehouseListByType(fulfillmentType, status));
    }


    /**
     * 根据城市+区域查询城配仓
     *
     * @return
     */
    @PostMapping(value = "/queryWarehouseByArea")
    public CommonResult<ExpressCityWarehouseVO> queryWarehouseByArea(@RequestBody WarehouseQueryDTO warehouseQueryDTO) {
        return CommonResult.ok(merchantStoreService.queryWarehouseByArea(warehouseQueryDTO));
    }

}
