package com.cosfo.oms.bill.model.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 分账配置保存请求VO
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
public class ProfitSharingConfigSaveVO {

    /**
     * 主键ID（更新时需要）
     */
    private Long id;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /**
     * 分账模式 1、延迟分账 2、实时分账
     */
    @NotNull(message = "分账模式不能为空")
    private Integer profitSharingMode;

    /**
     * 分账开关 0、关闭 1、开启
     */
    @NotNull(message = "分账开关不能为空")
    private Integer profitSharingSwitch;
}
