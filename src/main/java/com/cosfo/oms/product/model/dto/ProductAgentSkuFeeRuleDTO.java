package com.cosfo.oms.product.model.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ProductAgentSkuFeeRuleDTO {
    @NotNull
    private Long tenantId;
    /**
     * 收费规则0按比例1按件数
     */
    @NotNull
    private Integer type;
    /**
     * 规则明细
     */
    @NotEmpty
    private List<ProductAgentSkuFeeRuleDetailDTO> details;

    /**
     * @see com.cosfo.oms.common.context.ProductAutomaticIncreasePriceFlagEnum
     */
    private Integer automaticIncreasePriceFlag;
}
