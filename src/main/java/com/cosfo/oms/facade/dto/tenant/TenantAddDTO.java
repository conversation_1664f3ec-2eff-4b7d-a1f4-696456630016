package com.cosfo.oms.facade.dto.tenant;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class TenantAddDTO implements Serializable {
    /**
     * 品牌商城ID
     */
    private Long tenantId;

    /**
     * 鲜沐大客户Id
     */
    private Long adminId;

    /**
     * 租户类型：0-品牌方,1-鲜沐,2-帆台,3-外单
     */
    private Integer type;

    /**
     * 登录手机号
     */
    private String loginPhone;

    /**
     * 商城名称
     */
    private String merchantName;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 租户联系人名称
     */
    private String contactName;

    /**
     * 信用代码
     */
    private String creditCode;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 街道地址
     */
    private String address;

    /**
     * 公司联系手机号
     */
    private String companyPhone;

    /**
     * 联系电话-区号
     */
    private String companyAreaPhone;

    /**
     * 分账开关0关1开
     */
    private Integer profitSharingSwitch;

    /**
     * 分账渠道 0 微信 1 汇付
     */
    private Integer onlinePayChannel;

    /**
     * 操作人authUserId
     */
    private Long opAuthUserId;

    /**
     * 操作人id
     */
    private Long opUid;

    /**
     * 操作人名称
     */
    private String opUname;

    /**
     * 系统来源,这里是透传给auth用的
     * 之所以额外弄一个字段是为了平衡之前oms，manage对接auth的一些问题
     */
    private Integer systemOrigin;

    /**
     * 租户权益
     */
    private Map<Integer, LocalDate> purviewExpiredMap;

    /**
     * 登陆密码
     */
    private String loginPassword;

    /**
     * email
     */
    private String email;

    /**
     * 租户账户类型:0-手机号登录，1-邮箱登录
     */
    private Integer accountLoginType;
}
