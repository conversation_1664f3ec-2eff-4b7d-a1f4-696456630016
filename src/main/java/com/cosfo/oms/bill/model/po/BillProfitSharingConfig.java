package com.cosfo.oms.bill.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 分账配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Getter
@Setter
@TableName("bill_profit_sharing_config")
public class BillProfitSharingConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 1、延迟分账 2、实时分账
     */
    private Integer profitSharingMode;

    /**
     * 分账开关 0、关闭 1、开启
     */
    private Integer profitSharingSwitch;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;


}
