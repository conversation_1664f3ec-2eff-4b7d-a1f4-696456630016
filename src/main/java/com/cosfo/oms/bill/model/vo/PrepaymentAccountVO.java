package com.cosfo.oms.bill.model.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PrepaymentAccountVO implements Serializable {


    /**
     * 商城ID
     */
    private Long tenantId;

    /**
     * 商城名称
     */
    private String shopName;

    /**
     * 付款人
     */
    private String tenantName;

    /**
     * 可用类型 0、供应商直供 1、代仓费用 2、供应商直供和代仓费用
     */
    private Integer type;

    private String payTargetTypeDesc;

    /**
     * 持有余额
     */
    private BigDecimal totalAmount;


    /**
     * 余额最近变动时间
     */
    private LocalDateTime lastChangeTime;

}
