package com.cosfo.oms.bill.converter;

import com.cosfo.oms.bill.model.dto.ProfitSharingConfigDTO;
import com.cosfo.oms.bill.model.po.BillProfitSharingConfig;
import com.cosfo.oms.bill.model.vo.ProfitSharingConfigVO;
import org.springframework.beans.BeanUtils;

/**
 * 分账配置转换类
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
public class ProfitSharingConfigConverter {

    /**
     * VO转DTO
     *
     * @param vo ProfitSharingConfigVO
     * @return ProfitSharingConfigDTO
     */
    public static ProfitSharingConfigDTO voToDto(ProfitSharingConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ProfitSharingConfigDTO dto = new ProfitSharingConfigDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }

    /**
     * DTO转VO
     *
     * @param dto ProfitSharingConfigDTO
     * @return ProfitSharingConfigVO
     */
    public static ProfitSharingConfigVO dtoToVo(ProfitSharingConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        ProfitSharingConfigVO vo = new ProfitSharingConfigVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    /**
     * PO转DTO
     *
     * @param po BillProfitSharingConfig
     * @return ProfitSharingConfigDTO
     */
    public static ProfitSharingConfigDTO poToDto(BillProfitSharingConfig po) {
        if (po == null) {
            return null;
        }
        ProfitSharingConfigDTO dto = new ProfitSharingConfigDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    /**
     * DTO转PO
     *
     * @param dto ProfitSharingConfigDTO
     * @return BillProfitSharingConfig
     */
    public static BillProfitSharingConfig dtoToPo(ProfitSharingConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        BillProfitSharingConfig po = new BillProfitSharingConfig();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * 创建默认配置DTO
     *
     * @param tenantId 租户ID
     * @return ProfitSharingConfigDTO
     */
    public static ProfitSharingConfigDTO createDefaultConfig(Long tenantId) {
        ProfitSharingConfigDTO dto = new ProfitSharingConfigDTO();
        dto.setTenantId(tenantId);
        dto.setProfitSharingMode(1); // 默认延迟分账
        dto.setProfitSharingSwitch(0); // 默认关闭
        return dto;
    }
}
