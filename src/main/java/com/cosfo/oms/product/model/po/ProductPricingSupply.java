package com.cosfo.oms.product.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * product_pricing_supply
 *
 * <AUTHOR>
@Data
public class ProductPricingSupply implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * 供应SKU id
     */
    private Long supplySkuId;

    /**
     * 供应商tenantId
     */
    private Long supplyTenantId;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否关联商品
     */
    private Integer associated;
    private static final long serialVersionUID = 1L;
}