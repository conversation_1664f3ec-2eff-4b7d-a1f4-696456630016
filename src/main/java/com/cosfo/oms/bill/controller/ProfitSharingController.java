package com.cosfo.oms.bill.controller;


import com.cosfo.oms.bill.converter.ProfitSharingConfigConverter;
import com.cosfo.oms.bill.mapper.dao.BillProfitSharingDao;
import com.cosfo.oms.bill.model.dto.ProfitSharingConfigDTO;
import com.cosfo.oms.bill.model.po.BillProfitSharing;
import com.cosfo.oms.bill.model.vo.ProfitSharingConfigQueryVO;
import com.cosfo.oms.bill.model.vo.ProfitSharingConfigSaveVO;
import com.cosfo.oms.bill.model.vo.ProfitSharingConfigVO;
import com.cosfo.oms.bill.model.vo.ProfitSharingRuleVO;
import com.cosfo.oms.bill.service.ProfitSharingConfigService;
import com.cosfo.oms.bill.service.ProfitSharingRuleService;
import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.bill.model.dto.ProfitSharingRuleDTO;
import com.cosfo.oms.testcodde.dao.TestcodeDao;
import com.cosfo.oms.testcodde.model.Testcode;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 描述: 分账
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/26
 */
@RestController
@RequestMapping(value = "profit-sharing")
public class ProfitSharingController {
    @Resource
    private ProfitSharingRuleService service;
    @Resource
    private ProfitSharingConfigService profitSharingConfigService;
    @Resource
    private TestcodeDao dao;

    /**
     * 分账规则列表
     *
     * @param tenantId 商城Id
     * @return ResultDTO<List < ProfitSharingRuleVO>>
     */
    @PostMapping("/query/rule-list")
    public ResultDTO<List<ProfitSharingRuleVO>> ruleList(@RequestBody Long tenantId) {
        List<ProfitSharingRuleVO> list = service.ruleList(tenantId);
        return ResultDTO.success(list);
    }

    /**
     * 修改分账规则
     *
     * @param profitSharingRuleDtos 分账规则
     * @return
     */
    @PostMapping("/upsert/update-rule")
    public ResultDTO<Boolean> updateRule(@RequestBody List<ProfitSharingRuleDTO> profitSharingRuleDtos) {
        service.initRule(profitSharingRuleDtos);
        return ResultDTO.success();
    }

    /**
     * 分账规则列表
     */
    @GetMapping("/query/test")
    public ResultDTO ruleList() {
        Testcode byId = dao.getById(1);
        System.out.println(byId);
        return ResultDTO.success();
    }

    @PostMapping("/upsert/init/no-warehouse-rule")
    public CommonResult initNoWarehouseRule(@RequestBody List<Long> tenantIds) {
        service.initNoWarehouseRule(tenantIds);
        return CommonResult.ok();
    }

    /**
     * 查询分账配置
     *
     * @param queryVO 查询参数
     * @return ResultDTO<ProfitSharingConfigVO>
     */
    @PostMapping("/query/config")
    public ResultDTO<ProfitSharingConfigVO> queryConfig(@Valid @RequestBody ProfitSharingConfigQueryVO queryVO) {
        // Service层处理业务逻辑，返回DTO
        ProfitSharingConfigDTO configDTO = profitSharingConfigService.getConfigByTenantId(queryVO.getTenantId());

        // 转换为VO返回给前端
        ProfitSharingConfigVO configVO = ProfitSharingConfigConverter.dtoToVo(configDTO);

        return ResultDTO.success(configVO);
    }
}
