package com.cosfo.oms.market.model.vo;

import lombok.Data;


/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/11
 */
@Data
public class MarketSpuVO {
    /**
     * spuId
     */
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 二级分类Id
     */
    private Long secondClassificationId;
    /**
     * 二级分类
     */
    private String secondClassificationName;
    /**
     * 一级分类Id
     */
    private Long firstClassificationId;
    /**
     * 一级分类名称
     */
    private String firstClassificationName;
    /**
     * 类目Id
     */
    private Long firstCategoryId;
    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目Id
     */
    private Long secondCategoryId;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目Id
     */
    private Long thirdCategoryId;
    /**
     * 三级类目
     */
    private String thirdCategory;
}
