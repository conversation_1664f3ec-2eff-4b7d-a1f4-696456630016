package com.cosfo.oms.tenant.controller;


import com.cosfo.oms.tenant.model.dto.TenantPrivilegesConfigDTO;
import com.cosfo.oms.tenant.model.dto.TenantPrivilegesConfigQueryDTO;
import com.cosfo.oms.tenant.model.vo.TenantPrivilegesConfigDetailVO;
import com.cosfo.oms.tenant.model.vo.TenantPrivilegesSimpleConfigVO;
import com.cosfo.oms.tenant.service.TenantPrivilegesConfigService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 租户权益配置
 * <AUTHOR>
 * @since 2023-12-26
 */
@RestController
@RequestMapping("/tenant/tenantPrivilegesConfig")
public class TenantPrivilegesConfigController {

    @Resource
    private TenantPrivilegesConfigService tenantPrivilegesConfigService;
    /**
     * 获取当前租户权益配置
     */
    @PostMapping("/query")
    public CommonResult<TenantPrivilegesSimpleConfigVO> getPrivilegesConfig(@RequestBody TenantPrivilegesConfigQueryDTO queryDTO) {
        return CommonResult.ok(tenantPrivilegesConfigService.getTenantPrivileges(queryDTO.getTenantId()));
    }

    /**
     * 获取当前租户权益配置详情
     */
    @PostMapping("/query/detail")
    public CommonResult<TenantPrivilegesConfigDetailVO> getPrivilegesConfigDetail(@RequestBody TenantPrivilegesConfigQueryDTO queryDTO) {
        TenantPrivilegesConfigDetailVO tenantPrivilegesDetail = tenantPrivilegesConfigService.getTenantPrivilegesDetail(queryDTO.getTenantId());
        return CommonResult.ok(tenantPrivilegesDetail);
    }

    /**
     * 更新当前租户权益配置
     */
    @PostMapping("/upsert")
    public CommonResult<Boolean> upsertPrivilegesConfig(@RequestBody TenantPrivilegesConfigDTO configDTO) {
        tenantPrivilegesConfigService.updateTenantPrivileges(configDTO, configDTO.getTenantId());
        return CommonResult.ok();
    }
}

