package com.cosfo.oms.product.service;

import com.cosfo.oms.product.model.dto.ProductAgentSkuFeeRuleDTO;
import com.cosfo.oms.product.model.vo.ProductAgentSkuFeeRuleVO;
import net.xianmu.common.result.CommonResult;

/**
 * 代仓品规则
 */
public interface ProductAgentSkuFeeRuleService {
    /**
     * 查询代仓分账规则
     *
     * @param tenantId
     * @return
     */
    ProductAgentSkuFeeRuleVO detail(Long tenantId);

    /**
     * 设置代仓分账规则
     *
     * @param dto
     */
    void initRule(ProductAgentSkuFeeRuleDTO dto);
}
