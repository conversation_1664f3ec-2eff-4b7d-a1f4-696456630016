package com.cosfo.oms.wechat.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.oms.common.constant.Constants;
import com.cosfo.oms.wechat.api.WxaApi;
import com.cosfo.oms.wechat.bean.wxa.PluginListResult;
import com.cosfo.oms.wechat.model.dto.PluginDTO;
import com.cosfo.oms.wechat.service.WeChatAppletPluginService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-26
 * @Description:
 */
@Service
@Slf4j
public class WeChatAppletPluginServiceImpl implements WeChatAppletPluginService {

    @Override
    public List<PluginListResult.PluginResult> queryDevPlugin(String accessToken, String pluginAppId) {
        return plugin(accessToken, pluginAppId, PluginDTO.LIST_ACTION, null);
    }


    private List<PluginListResult.PluginResult> plugin(String accessToken, String pluginAppId, String action, String reason) {
        PluginDTO pluginDTO = new PluginDTO();
        pluginDTO.setAction(action);
        pluginDTO.setPlugin_appid(pluginAppId);
        pluginDTO.setReason(reason);
        if (PluginDTO.APPLY_ACTION.equals(action)) {
            pluginDTO.setUser_version(Constants.QIYU_VERSION);
        }
        PluginListResult pluginListResult = WxaApi.plugin(accessToken, pluginDTO);
        if (!pluginListResult.successed()) {
            log.info("调用微信请求插件接口异常, pluginListResult:{}", JSON.toJSONString(pluginListResult));
            throw new ProviderException(pluginListResult.getErrcode() + pluginListResult.getErrmsg());
        }
        return pluginListResult.getPlugin_list();
    }

    @Override
    public void applyDevPlugin(String accessToken, String pluginAppId) {
        plugin(accessToken, pluginAppId, PluginDTO.APPLY_ACTION, PluginDTO.DEFAULT_REASON);
    }

}
