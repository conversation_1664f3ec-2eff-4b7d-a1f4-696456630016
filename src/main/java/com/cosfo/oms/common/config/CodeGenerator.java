package com.cosfo.oms.common.config;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.mysql.cj.jdbc.MysqlDataSource;

import java.util.Collections;

/**
 * 代码生成器
 *
 * @author: Cathy
 */
public class CodeGenerator {

    private final static String hostIpAddress = "************";

    public static void main(String[] args) {
        String projectPath = System.getProperty("user.dir");
        MysqlDataSource mysqlDataSource = new MysqlDataSource();
        mysqlDataSource.setUrl("****************************************************************************************");
        mysqlDataSource.setUser("test");
        mysqlDataSource.setPassword("xianmu619");
        FastAutoGenerator.create(new DataSourceConfig.Builder(mysqlDataSource))
                .globalConfig(builder -> {
                    builder.author("George") // 设置作者
//                            .enableSwagger() // 开启 swagger 模式
                            .disableOpenDir()
                            .fileOverride() // 覆盖已生成文件
                            .outputDir(projectPath + "/" + "/src/main/java"); // 指定输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.cosfo.oms") // 设置父包名
                            .moduleName("bill") // 设置父包模块名
                            .entity("model.po")
                            .service("dao")
                            .serviceImpl("dao.impl")
                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml, projectPath + "/" + "/src/main/resources/mapper" + "/bill")); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude("bill_profit_sharing_config") // 设置需要生成的表名
                            .entityBuilder()
                            .enableLombok()
                            .serviceBuilder()
                            .formatServiceFileName("%sDao")
                            .formatServiceImplFileName("%sDaoImpl")
                    ; // 设置过滤表前缀
                })
//                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
