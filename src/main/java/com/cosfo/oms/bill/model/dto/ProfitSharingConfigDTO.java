package com.cosfo.oms.bill.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分账配置DTO
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
public class ProfitSharingConfigDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 分账模式 1、延迟分账 2、实时分账
     */
    private Integer profitSharingMode;

    /**
     * 分账开关 0、关闭 1、开启
     */
    private Integer profitSharingSwitch;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;
}
