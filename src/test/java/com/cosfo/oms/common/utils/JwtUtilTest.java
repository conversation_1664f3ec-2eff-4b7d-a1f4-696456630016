package com.cosfo.oms.common.utils;

import org.junit.jupiter.api.Test;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/27
 */
class JwtUtilTest {

    @Test
    void createToken() {
        String token = JwtUtil.createToken(4L);
        System.out.println(token);
        Long aLong = JwtUtil.verifyToken(token);
        System.out.println(aLong);
    }

    @Test
    void verifyToken() {
    }

    @Test
    void encrypt() throws Exception {
        String encrypt = AESUtils.encrypt("2");
        System.out.println("加密token" + encrypt);
        System.out.println("解密token"  + AESUtils.decrypt(encrypt));
    }
}