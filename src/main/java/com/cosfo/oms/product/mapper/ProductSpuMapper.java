package com.cosfo.oms.product.mapper;

import com.cosfo.oms.product.model.dto.ProductPricingSupplyQueryDTO;
import com.cosfo.oms.product.model.dto.SupplySkuQueryDTO;
import com.cosfo.oms.product.model.po.ProductSpu;
import com.cosfo.oms.product.model.vo.ProductSpuVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductSpuMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductSpu record);

    int insertSelective(ProductSpu record);

    ProductSpu selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductSpu record);

    int updateByPrimaryKey(ProductSpu record);

    /**
     * 根据类目Id查询商品
     *
     * @param categoryIds
     * @return
     */
    List<ProductSpu> selectByCategoryIds(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 根据品牌Id查询商品
     *
     * @param brandId
     * @return
     */
    List<ProductSpu> selectByBrandId(@Param("brandId") Long brandId);

    /**
     * 查询报价单列表
     *
     * @param productPricingSupplyQueryDTO
     * @return
     */
    List<ProductSpuVO> queryPricingSupplyList(@Param("productPricingSupplyQueryDTO") ProductPricingSupplyQueryDTO productPricingSupplyQueryDTO);

    /**
     * 查询商品信息
     *
     * @param supplySkuIds
     * @return
     */
    List<ProductSpuVO> queryBySupplySkuIds(@Param("supplySkuIds") List<Long> supplySkuIds);

    /**
     * 查询供应商报价单信息
     *
     * @param supplySkuQueryDTO
     * @param tenantIds
     * @return
     */
    List<ProductSpuVO> queryBySupplySkuQueryDTO(@Param("supplySkuQueryDTO") SupplySkuQueryDTO supplySkuQueryDTO, @Param("tenantIds") List<Long> tenantIds);
}