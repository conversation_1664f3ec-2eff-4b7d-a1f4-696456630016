package com.cosfo.oms.order.service;

import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.order.model.po.OrderQueryDTO;
import com.cosfo.oms.order.model.vo.OrderDetailDeliveryVO;
import com.cosfo.oms.order.model.vo.OrderVO;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/2 9:12
 */
public interface OrderBusinessService {

    /**
     * 列表
     *
     * @param orderQueryDTO
     * @return
     */
    ResultDTO<PageInfo<OrderVO>> list(OrderQueryDTO orderQueryDTO);

    /**
     * 详情
     *
     * @param orderId
     * @return
     */
    ResultDTO<OrderVO> detail(Long orderId);

    /**
     * 获取订单详情配送详信息
     * @param orderId
     * @return
     */
    OrderDetailDeliveryVO deliveryDetails(Long orderId);

    /**
     * 订单导出
     *
     * @param orderQueryDTO
     */
    void export(OrderQueryDTO orderQueryDTO);
}
