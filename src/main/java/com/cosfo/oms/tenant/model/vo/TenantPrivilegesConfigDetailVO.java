package com.cosfo.oms.tenant.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TenantPrivilegesConfigDetailVO implements Serializable {

    /**
     * 当前版本
     */
    private Integer saleVersion;

    /**
     * 到期时间
     */
    private LocalDate expireDate;

    /**
     * 客户经理
     */
    private String customerManager;

    /**
     * 版本id
     */
    private Long functionSetId;

    /**
     * 增购列表
     */
    private List<TenantPrivilegesConfigVO> privilegesConfigList;

    /**
     * 货币符号
     */
    private Integer currency;
}
