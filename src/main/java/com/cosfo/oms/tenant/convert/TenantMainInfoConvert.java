package com.cosfo.oms.tenant.convert;

import com.cosfo.oms.tenant.model.dto.*;
import com.cosfo.oms.tenant.model.po.Tenant;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import com.cosfo.oms.tenant.model.po.TenantCompany;
import com.cosfo.oms.tenant.model.vo.TenantAuthVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: monna.chen
 * @Date: 2023/3/28 15:29
 * @Description:
 */
@Mapper
public interface TenantMainInfoConvert {
    TenantMainInfoConvert INSTANCE = Mappers.getMapper(TenantMainInfoConvert.class);

    TenantAddMainInfoServiceDTO convert2ServiceDto(TenantAddMainInfoDTO dto);

    @Mapping(source = "loginPhone", target = "phone")
    @Mapping(source = "merchantName", target = "tenantName")
    Tenant convert2Tenant(TenantAddMainInfoServiceDTO dto);

    TenantCompany convert2TenantCompany(TenantAddMainInfoServiceDTO dto);

    TenantAuthVO convert2AuthVo(TenantAuthConnection entity);

    TenantAuthConnection convert2Entity(TenantAuthServiceDTO dto);

    TenantAuthConnection convert2Entity(TenantAuthAppIdDTO dto);

    TenantAuthServiceDTO convert2ServiceDto(TenantAuthDTO dto);

    TenantAuthServiceDTO convert2ServiceDto(TenantAuthConnection entity);


}
