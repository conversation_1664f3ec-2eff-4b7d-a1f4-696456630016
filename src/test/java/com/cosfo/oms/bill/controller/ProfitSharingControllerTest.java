package com.cosfo.oms.bill.controller;

import com.cosfo.oms.bill.model.dto.ProfitSharingConfigDTO;
import com.cosfo.oms.bill.model.vo.ProfitSharingConfigQueryVO;
import com.cosfo.oms.bill.model.vo.ProfitSharingConfigSaveVO;
import com.cosfo.oms.bill.model.vo.ProfitSharingConfigVO;
import com.cosfo.oms.bill.service.ProfitSharingConfigService;
import com.cosfo.oms.common.result.ResultDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 分账配置Controller测试类
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@ExtendWith(MockitoExtension.class)
@WebMvcTest(ProfitSharingController.class)
class ProfitSharingControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProfitSharingConfigService profitSharingConfigService;

    @Autowired
    private ObjectMapper objectMapper;

    private Long testTenantId;
    private ProfitSharingConfigDTO testConfigDTO;

    @BeforeEach
    void setUp() {
        testTenantId = 1001L;
        testConfigDTO = new ProfitSharingConfigDTO();
        testConfigDTO.setId(1L);
        testConfigDTO.setTenantId(testTenantId);
        testConfigDTO.setProfitSharingMode(2);
        testConfigDTO.setProfitSharingSwitch(1);
        testConfigDTO.setCreateTime(LocalDateTime.now());
        testConfigDTO.setUpdateTime(LocalDateTime.now());
        testConfigDTO.setCreatedBy("test");
        testConfigDTO.setUpdatedBy("test");
    }

    @Test
    void testQueryConfig_Success() throws Exception {
        // Given
        ProfitSharingConfigQueryVO queryVO = new ProfitSharingConfigQueryVO();
        queryVO.setTenantId(testTenantId);

        when(profitSharingConfigService.getConfigByTenantId(eq(testTenantId)))
                .thenReturn(testConfigDTO);

        // When & Then
        mockMvc.perform(post("/profit-sharing/query/config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryVO)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.tenantId").value(testTenantId))
                .andExpect(jsonPath("$.data.profitSharingMode").value(2))
                .andExpect(jsonPath("$.data.profitSharingSwitch").value(1));
    }

    @Test
    void testQueryConfig_DefaultConfig() throws Exception {
        // Given
        ProfitSharingConfigQueryVO queryVO = new ProfitSharingConfigQueryVO();
        queryVO.setTenantId(testTenantId);

        ProfitSharingConfigDTO defaultConfigDTO = new ProfitSharingConfigDTO();
        defaultConfigDTO.setTenantId(testTenantId);
        defaultConfigDTO.setProfitSharingMode(1); // 默认延迟分账
        defaultConfigDTO.setProfitSharingSwitch(0); // 默认关闭

        when(profitSharingConfigService.getConfigByTenantId(eq(testTenantId)))
                .thenReturn(defaultConfigDTO);

        // When & Then
        mockMvc.perform(post("/profit-sharing/query/config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryVO)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.tenantId").value(testTenantId))
                .andExpect(jsonPath("$.data.profitSharingMode").value(1))
                .andExpect(jsonPath("$.data.profitSharingSwitch").value(0));
    }

    @Test
    void testQueryConfig_InvalidRequest() throws Exception {
        // Given - 缺少tenantId
        ProfitSharingConfigQueryVO queryVO = new ProfitSharingConfigQueryVO();

        // When & Then
        mockMvc.perform(post("/profit-sharing/query/config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryVO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testSaveOrUpdateConfig_NewConfig() throws Exception {
        // Given
        ProfitSharingConfigSaveVO saveVO = new ProfitSharingConfigSaveVO();
        saveVO.setTenantId(testTenantId);
        saveVO.setProfitSharingMode(2);
        saveVO.setProfitSharingSwitch(1);
        saveVO.setCreatedBy("testUser");
        saveVO.setUpdatedBy("testUser");

        when(profitSharingConfigService.saveOrUpdateConfig(any(ProfitSharingConfigDTO.class)))
                .thenReturn(testConfigDTO);

        // When & Then
        mockMvc.perform(post("/profit-sharing/upsert/config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(saveVO)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.tenantId").value(testTenantId))
                .andExpect(jsonPath("$.data.profitSharingMode").value(2))
                .andExpect(jsonPath("$.data.profitSharingSwitch").value(1));
    }

    @Test
    void testSaveOrUpdateConfig_UpdateExisting() throws Exception {
        // Given
        ProfitSharingConfigSaveVO saveVO = new ProfitSharingConfigSaveVO();
        saveVO.setId(1L);
        saveVO.setTenantId(testTenantId);
        saveVO.setProfitSharingMode(1);
        saveVO.setProfitSharingSwitch(0);
        saveVO.setUpdatedBy("updateUser");

        ProfitSharingConfigDTO updatedConfigDTO = new ProfitSharingConfigDTO();
        updatedConfigDTO.setId(1L);
        updatedConfigDTO.setTenantId(testTenantId);
        updatedConfigDTO.setProfitSharingMode(1);
        updatedConfigDTO.setProfitSharingSwitch(0);
        updatedConfigDTO.setUpdatedBy("updateUser");

        when(profitSharingConfigService.saveOrUpdateConfig(any(ProfitSharingConfigDTO.class)))
                .thenReturn(updatedConfigDTO);

        // When & Then
        mockMvc.perform(post("/profit-sharing/upsert/config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(saveVO)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.tenantId").value(testTenantId))
                .andExpect(jsonPath("$.data.profitSharingMode").value(1))
                .andExpect(jsonPath("$.data.profitSharingSwitch").value(0));
    }

    @Test
    void testSaveOrUpdateConfig_InvalidRequest_MissingTenantId() throws Exception {
        // Given - 缺少tenantId
        ProfitSharingConfigSaveVO saveVO = new ProfitSharingConfigSaveVO();
        saveVO.setProfitSharingMode(2);
        saveVO.setProfitSharingSwitch(1);

        // When & Then
        mockMvc.perform(post("/profit-sharing/upsert/config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(saveVO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testSaveOrUpdateConfig_InvalidRequest_MissingMode() throws Exception {
        // Given - 缺少profitSharingMode
        ProfitSharingConfigSaveVO saveVO = new ProfitSharingConfigSaveVO();
        saveVO.setTenantId(testTenantId);
        saveVO.setProfitSharingSwitch(1);

        // When & Then
        mockMvc.perform(post("/profit-sharing/upsert/config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(saveVO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testSaveOrUpdateConfig_InvalidRequest_MissingSwitch() throws Exception {
        // Given - 缺少profitSharingSwitch
        ProfitSharingConfigSaveVO saveVO = new ProfitSharingConfigSaveVO();
        saveVO.setTenantId(testTenantId);
        saveVO.setProfitSharingMode(2);

        // When & Then
        mockMvc.perform(post("/profit-sharing/upsert/config")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(saveVO)))
                .andExpect(status().isBadRequest());
    }
}
